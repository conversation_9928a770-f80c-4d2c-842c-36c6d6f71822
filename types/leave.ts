import mongoose from 'mongoose';

/**
 * Interface for leave type document
 */
export interface ILeaveType {
  _id: string | mongoose.Types.ObjectId;
  name: string;
  code: string;
  description?: string;
  defaultDays: number;
  isActive: boolean;
  isPaid: boolean;
  requiresApproval: boolean;
  maxConsecutiveDays: number;
  minNoticeInDays: number;
  allowCarryOver: boolean;
  maxCarryOverDays: number;
  color?: string;
  applicableRoles?: string[];
  applicableDepartments?: mongoose.Types.ObjectId[];
  createdBy: mongoose.Types.ObjectId;
  updatedBy?: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Interface for leave request document
 */
export interface ILeave {
  _id: string | mongoose.Types.ObjectId;
  leaveId: string;
  employeeId: mongoose.Types.ObjectId;
  leaveTypeId: mongoose.Types.ObjectId;
  startDate: Date;
  endDate: Date;
  duration: number;
  reason?: string;
  status: 'pending' | 'approved' | 'rejected' | 'cancelled';
  attachments?: string[];
  notes?: string;
  approvedBy?: mongoose.Types.ObjectId;
  approvalDate?: Date;
  rejectionReason?: string;
  createdBy: mongoose.Types.ObjectId;
  updatedBy?: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Interface for leave balance document
 */
export interface ILeaveBalance {
  _id: string | mongoose.Types.ObjectId;
  employeeId: mongoose.Types.ObjectId;
  leaveTypeId: mongoose.Types.ObjectId;
  year: number;
  totalDays: number;
  usedDays: number;
  pendingDays: number;
  remainingDays: number;
  carryOverDays: number;
  createdBy: mongoose.Types.ObjectId;
  updatedBy?: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Interface for leave encashment document
 */
export interface ILeaveEncashment {
  _id: string | mongoose.Types.ObjectId;
  encashmentId: string;
  employeeId: mongoose.Types.ObjectId;
  leaveTypeId: mongoose.Types.ObjectId;
  encashmentRuleId: mongoose.Types.ObjectId;
  daysEncashed: number;
  ratePerDay: number;
  totalAmount: number;
  encashmentDate: Date;
  status: 'pending' | 'approved' | 'rejected' | 'processed';
  approvedBy?: mongoose.Types.ObjectId;
  approvalDate?: Date;
  rejectionReason?: string;
  payrollRunId?: mongoose.Types.ObjectId;
  payrollProcessedDate?: Date;
  createdBy: mongoose.Types.ObjectId;
  updatedBy?: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Client-safe leave type interface (without mongoose dependencies)
 */
export interface LeaveType {
  id: string;
  name: string;
  code: string;
  description?: string;
  defaultDays: number;
  isActive: boolean;
  isPaid: boolean;
  requiresApproval: boolean;
  maxConsecutiveDays: number;
  minNoticeInDays: number;
  allowCarryOver: boolean;
  maxCarryOverDays: number;
  color?: string;
  applicableRoles?: string[];
  applicableDepartments?: string[];
  createdBy: string;
  updatedBy?: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * Client-safe leave request interface
 */
export interface LeaveRequest {
  id: string;
  leaveId: string;
  employeeId: string;
  leaveTypeId: string;
  startDate: string;
  endDate: string;
  duration: number;
  reason?: string;
  status: 'pending' | 'approved' | 'rejected' | 'cancelled';
  attachments?: string[];
  notes?: string;
  approvedBy?: string;
  approvalDate?: string;
  rejectionReason?: string;
  createdBy: string;
  updatedBy?: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * Client-safe leave balance interface
 */
export interface LeaveBalance {
  id: string;
  employeeId: string;
  leaveTypeId: string;
  year: number;
  totalDays: number;
  usedDays: number;
  pendingDays: number;
  remainingDays: number;
  carryOverDays: number;
  createdBy: string;
  updatedBy?: string;
  createdAt: string;
  updatedAt: string;
}
