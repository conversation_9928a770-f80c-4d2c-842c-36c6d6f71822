"use client"

import { useState, useEffect, useCallback } from 'react';
import { useToast } from '@/hooks/use-toast';
import { LeaveType } from '@/types/leave';

// Re-export LeaveType for convenience
export { LeaveType } from '@/types/leave';

interface UseLeaveTypesOptions {
  activeOnly?: boolean;
  autoFetch?: boolean;
}

interface UseLeaveTypesReturn {
  leaveTypes: LeaveType[];
  loading: boolean;
  error: string | null;
  fetchLeaveTypes: (activeOnly?: boolean) => Promise<void>;
  refreshLeaveTypes: () => Promise<void>;
  createLeaveType: (data: any) => Promise<LeaveType | null>;
  updateLeaveType: (id: string, data: any) => Promise<LeaveType | null>;
  deleteLeaveType: (id: string) => Promise<boolean>;
}

export function useLeaveTypes(options: UseLeaveTypesOptions = {}): UseLeaveTypesReturn {
  const { activeOnly = true, autoFetch = true } = options;
  const { toast } = useToast();

  const [leaveTypes, setLeaveTypes] = useState<LeaveType[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentActiveOnly, setCurrentActiveOnly] = useState(activeOnly);

  const fetchLeaveTypes = useCallback(async (fetchActiveOnly: boolean = currentActiveOnly) => {
    try {
      setLoading(true);
      setError(null);
      setCurrentActiveOnly(fetchActiveOnly);

      const params = new URLSearchParams();
      if (fetchActiveOnly) {
        params.append('activeOnly', 'true');
      }

      const response = await fetch(`/api/leave/types?${params.toString()}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch leave types');
      }

      const result = await response.json();

      // Handle both paginated and non-paginated responses
      if (result.data && Array.isArray(result.data)) {
        setLeaveTypes(result.data);
      } else if (Array.isArray(result)) {
        setLeaveTypes(result);
      } else {
        setLeaveTypes([]);
      }

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred while fetching leave types';
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }, [currentActiveOnly, toast]);

  const refreshLeaveTypes = useCallback(async () => {
    await fetchLeaveTypes(currentActiveOnly);
  }, [fetchLeaveTypes, currentActiveOnly]);

  const createLeaveType = useCallback(async (data: any): Promise<LeaveType | null> => {
    try {
      setLoading(true);
      
      const response = await fetch('/api/leave/types', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create leave type');
      }

      const result = await response.json();
      
      toast({
        title: "Success",
        description: result.message || "Leave type created successfully",
      });

      // Refresh the list
      await refreshLeaveTypes();
      
      return result.data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred while creating leave type';
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      return null;
    } finally {
      setLoading(false);
    }
  }, [refreshLeaveTypes, toast]);

  const updateLeaveType = useCallback(async (id: string, data: any): Promise<LeaveType | null> => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/leave/types/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update leave type');
      }

      const result = await response.json();

      toast({
        title: "Success",
        description: result.message || "Leave type updated successfully",
      });

      // Refresh the list to get updated data
      await refreshLeaveTypes();

      return result.data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred while updating leave type';
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      return null;
    } finally {
      setLoading(false);
    }
  }, [toast, refreshLeaveTypes]);

  const deleteLeaveType = useCallback(async (id: string): Promise<boolean> => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/leave/types/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete leave type');
      }

      const result = await response.json();

      toast({
        title: "Success",
        description: result.message || "Leave type deleted successfully",
      });

      // Refresh the list to remove the deleted item
      await refreshLeaveTypes();

      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred while deleting leave type';
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      return false;
    } finally {
      setLoading(false);
    }
  }, [toast, refreshLeaveTypes]);

  // Auto-fetch on mount if enabled
  useEffect(() => {
    if (autoFetch) {
      fetchLeaveTypes(activeOnly);
    }
  }, [autoFetch]); // Only depend on autoFetch to avoid infinite loops

  return {
    leaveTypes,
    loading,
    error,
    fetchLeaveTypes,
    refreshLeaveTypes,
    createLeaveType,
    updateLeaveType,
    deleteLeaveType,
  };
}
