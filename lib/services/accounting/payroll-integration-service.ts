import mongoose from 'mongoose';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import Transaction from '@/models/accounting/Transaction';
import Account from '@/models/accounting/Account';
import JournalEntry from '@/models/accounting/JournalEntry';
import { format } from 'date-fns';

/**
 * Service for integrating payroll with accounting
 */
export class PayrollIntegrationService {
  /**
   * Create accounting entries for a payroll run
   * @param payrollRunId - ID of the payroll run
   * @param userId - ID of the user creating the entries
   * @returns Created journal entry
   */
  async createPayrollAccountingEntries(payrollRunId: string, userId: string): Promise<any> {
    try {
      await connectToDatabase();
      
      // Import PayrollRun model dynamically to avoid circular dependencies
      const PayrollRun = mongoose.model('PayrollRun');
      const PayrollItem = mongoose.model('PayrollItem');
      
      // Get payroll run with items
      const payrollRun = await PayrollRun.findById(payrollRunId)
        .populate({
          path: 'items',
          populate: [
            { path: 'employee', select: 'firstName lastName fullName employeeNumber department' },
            { path: 'department', select: 'name code' }
          ]
        });
      
      if (!payrollRun) {
        throw new Error(`Payroll run with ID ${payrollRunId} not found`);
      }
      
      // Check if journal entry already exists for this payroll run
      const existingJournalEntry = await JournalEntry.findOne({ 
        reference: `Payroll-${payrollRun.runNumber}`,
        'metadata.payrollRunId': payrollRunId
      });
      
      if (existingJournalEntry) {
        return existingJournalEntry;
      }
      
      // Get default accounts
      const salaryExpenseAccount = await Account.findOne({ 
        type: 'expense',
        defaultFor: 'salary'
      });
      
      const payrollLiabilityAccount = await Account.findOne({ 
        type: 'liability',
        defaultFor: 'payroll'
      });
      
      const taxLiabilityAccount = await Account.findOne({ 
        type: 'liability',
        defaultFor: 'tax'
      });
      
      const benefitsExpenseAccount = await Account.findOne({ 
        type: 'expense',
        defaultFor: 'benefits'
      });
      
      if (!salaryExpenseAccount || !payrollLiabilityAccount || !taxLiabilityAccount || !benefitsExpenseAccount) {
        throw new Error('Default accounts for payroll not configured');
      }
      
      // Prepare journal entry items
      const journalItems = [];
      
      // Group by department for salary expense
      const departmentTotals = {};
      
      // Process each payroll item
      for (const item of payrollRun.items) {
        // Group salary expenses by department
        const departmentId = item.department ? item.department._id.toString() : 'unassigned';
        if (!departmentTotals[departmentId]) {
          departmentTotals[departmentId] = {
            departmentId: item.department ? item.department._id : null,
            departmentName: item.department ? item.department.name : 'Unassigned',
            grossSalary: 0,
            tax: 0,
            deductions: 0,
            benefits: 0,
            netSalary: 0
          };
        }
        
        departmentTotals[departmentId].grossSalary += item.grossSalary || 0;
        departmentTotals[departmentId].tax += item.tax || 0;
        departmentTotals[departmentId].deductions += item.totalDeductions || 0;
        departmentTotals[departmentId].benefits += item.totalBenefits || 0;
        departmentTotals[departmentId].netSalary += item.netSalary || 0;
      }
      
      // Add salary expense entries by department
      for (const deptId in departmentTotals) {
        const dept = departmentTotals[deptId];
        
        // Salary expense (debit)
        journalItems.push({
          accountId: salaryExpenseAccount._id,
          description: `Salary Expense - ${dept.departmentName}`,
          debit: dept.grossSalary,
          credit: 0,
          departmentId: dept.departmentId
        });
        
        // Benefits expense (debit)
        if (dept.benefits > 0) {
          journalItems.push({
            accountId: benefitsExpenseAccount._id,
            description: `Benefits Expense - ${dept.departmentName}`,
            debit: dept.benefits,
            credit: 0,
            departmentId: dept.departmentId
          });
        }
      }
      
      // Add tax liability (credit)
      const totalTax = Object.values(departmentTotals).reduce((sum: number, dept: any) => sum + dept.tax, 0);
      if (totalTax > 0) {
        journalItems.push({
          accountId: taxLiabilityAccount._id,
          description: 'Tax Withholding Liability',
          debit: 0,
          credit: totalTax
        });
      }
      
      // Add payroll liability (credit)
      const totalNetSalary = Object.values(departmentTotals).reduce((sum: number, dept: any) => sum + dept.netSalary, 0);
      journalItems.push({
        accountId: payrollLiabilityAccount._id,
        description: 'Net Salary Payable',
        debit: 0,
        credit: totalNetSalary
      });
      
      // Create journal entry
      const journalEntry = new JournalEntry({
        date: payrollRun.paymentDate || new Date(),
        reference: `Payroll-${payrollRun.runNumber}`,
        description: `Payroll for ${format(new Date(payrollRun.periodStart), 'MMM yyyy')}`,
        items: journalItems,
        status: 'posted',
        metadata: {
          payrollRunId: payrollRun._id,
          payrollPeriod: {
            start: payrollRun.periodStart,
            end: payrollRun.periodEnd
          }
        },
        notes: `Automatically generated from Payroll Run #${payrollRun.runNumber}`,
        createdBy: userId,
        updatedBy: userId
      });
      
      await journalEntry.save();
      
      // Update payroll run with journal entry reference
      payrollRun.accountingEntryId = journalEntry._id;
      payrollRun.accountingStatus = 'processed';
      await payrollRun.save();
      
      return journalEntry;
    } catch (error) {
      logger.error('Error creating payroll accounting entries', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }

  /**
   * Generate payroll data for leave deductions and encashments
   * @param periodStart - Start date of the payroll period
   * @param periodEnd - End date of the payroll period
   * @returns Payroll leave data
   */
  async generatePayrollData(periodStart: Date, periodEnd: Date): Promise<any> {
    try {
      await connectToDatabase();
      logger.info('Generating payroll leave data', LogCategory.PAYROLL, { periodStart, periodEnd });

      // Import models dynamically to avoid circular dependencies
      const Employee = mongoose.model('Employee');
      const Leave = mongoose.model('Leave');
      const LeaveEncashment = mongoose.model('LeaveEncashment');
      const EmployeeSalary = mongoose.model('EmployeeSalary');

      // Get all active employees
      const employees = await Employee.find({ isActive: true })
        .populate('department', 'name code')
        .select('firstName lastName fullName employeeNumber department basicSalary');

      const leaveDeductions = [];
      const encashmentPayments = [];
      let totalEmployees = 0;
      let totalLeaveDeductions = 0;
      let totalEncashmentPayments = 0;

      for (const employee of employees) {
        totalEmployees++;

        // Get employee's current salary
        const employeeSalary = await EmployeeSalary.findOne({
          employeeId: employee._id,
          isActive: true
        }).sort({ effectiveDate: -1 });

        const basicSalary = employeeSalary?.basicSalary || employee.basicSalary || 0;
        const dailyRate = basicSalary / 30; // Assuming 30 days per month

        // Get leave requests for the period
        const leaveRequests = await Leave.find({
          employeeId: employee._id,
          startDate: { $gte: periodStart },
          endDate: { $lte: periodEnd },
          status: 'approved'
        }).populate('leaveTypeId', 'name isPaid');

        // Calculate leave deductions
        let totalUnpaidDays = 0;
        let totalDeductionAmount = 0;
        const employeeLeaveRequests = [];

        for (const leave of leaveRequests) {
          const isPaid = leave.leaveTypeId?.isPaid || false;
          const deductionAmount = isPaid ? 0 : leave.duration * dailyRate;

          if (!isPaid) {
            totalUnpaidDays += leave.duration;
            totalDeductionAmount += deductionAmount;
          }

          employeeLeaveRequests.push({
            leaveType: leave.leaveTypeId?.name || 'Unknown',
            startDate: leave.startDate.toISOString().split('T')[0],
            endDate: leave.endDate.toISOString().split('T')[0],
            days: leave.duration,
            isPaid,
            deductionAmount
          });
        }

        if (totalUnpaidDays > 0) {
          leaveDeductions.push({
            employeeId: employee._id.toString(),
            employeeName: employee.fullName || `${employee.firstName} ${employee.lastName}`,
            employeeCode: employee.employeeNumber,
            totalUnpaidDays,
            totalDeductionAmount,
            basicSalary,
            dailyRate,
            leaveRequests: employeeLeaveRequests
          });
          totalLeaveDeductions += totalDeductionAmount;
        }

        // Get leave encashments for the period
        const encashments = await LeaveEncashment.find({
          employeeId: employee._id,
          encashmentDate: { $gte: periodStart, $lte: periodEnd },
          status: 'approved'
        }).populate('leaveTypeId', 'name');

        if (encashments.length > 0) {
          let totalEncashmentAmount = 0;
          let taxableAmount = 0;
          let nonTaxableAmount = 0;
          const employeeEncashments = [];

          for (const encashment of encashments) {
            const amount = encashment.totalAmount || 0;
            totalEncashmentAmount += amount;

            // Determine if taxable (this logic may need adjustment based on business rules)
            const isTaxable = encashment.taxable !== false; // Default to taxable unless explicitly set to false
            if (isTaxable) {
              taxableAmount += amount;
            } else {
              nonTaxableAmount += amount;
            }

            employeeEncashments.push({
              encashmentId: encashment._id.toString(),
              leaveType: encashment.leaveTypeId?.name || 'Unknown',
              daysEncashed: encashment.daysEncashed,
              ratePerDay: encashment.ratePerDay,
              totalAmount: amount,
              taxable: isTaxable,
              payrollComponent: isTaxable ? 'Taxable Allowance' : 'Non-Taxable Benefit'
            });
          }

          encashmentPayments.push({
            employeeId: employee._id.toString(),
            employeeName: employee.fullName || `${employee.firstName} ${employee.lastName}`,
            employeeCode: employee.employeeNumber,
            totalEncashmentAmount,
            taxableAmount,
            nonTaxableAmount,
            encashments: employeeEncashments
          });
          totalEncashmentPayments += totalEncashmentAmount;
        }
      }

      const netPayrollImpact = totalEncashmentPayments - totalLeaveDeductions;

      return {
        periodStart: periodStart.toISOString().split('T')[0],
        periodEnd: periodEnd.toISOString().split('T')[0],
        totalEmployees,
        summary: {
          totalLeaveDeductions,
          totalEncashmentPayments,
          netPayrollImpact
        },
        leaveDeductions,
        encashmentPayments
      };

    } catch (error) {
      logger.error('Error generating payroll leave data', LogCategory.PAYROLL, error);
      throw error;
    }
  }

  /**
   * Analyze payroll budget variance
   * @param departmentId - Optional department ID to filter by
   * @param period - Period to analyze (e.g., { year: 2023, month: 5 })
   * @returns Variance analysis
   */
  async analyzePayrollBudgetVariance(
    departmentId?: string,
    period?: { year: number; month?: number; quarter?: number }
  ): Promise<any> {
    try {
      await connectToDatabase();

      // Import models dynamically
      const PayrollRun = mongoose.model('PayrollRun');
      const Budget = mongoose.model('Budget');
      const Department = mongoose.model('Department');

      // Build date range for the period
      let startDate: Date, endDate: Date;
      const now = new Date();

      if (!period) {
        // Default to current month
        const currentYear = now.getFullYear();
        const currentMonth = now.getMonth();
        startDate = new Date(currentYear, currentMonth, 1);
        endDate = new Date(currentYear, currentMonth + 1, 0);
      } else if (period.month) {
        // Specific month
        startDate = new Date(period.year, period.month - 1, 1);
        endDate = new Date(period.year, period.month, 0);
      } else if (period.quarter) {
        // Specific quarter
        const quarterStartMonth = (period.quarter - 1) * 3;
        startDate = new Date(period.year, quarterStartMonth, 1);
        endDate = new Date(period.year, quarterStartMonth + 3, 0);
      } else {
        // Full year
        startDate = new Date(period.year, 0, 1);
        endDate = new Date(period.year, 12, 0);
      }

      // Build query for payroll runs
      const payrollQuery: any = {
        status: 'completed',
        periodStart: { $gte: startDate },
        periodEnd: { $lte: endDate }
      };

      // Build query for budgets
      const budgetQuery: any = {
        type: 'payroll',
        year: period?.year || now.getFullYear()
      };

      // Filter by department if provided
      if (departmentId) {
        payrollQuery['items.department'] = new mongoose.Types.ObjectId(departmentId);
        budgetQuery.department = new mongoose.Types.ObjectId(departmentId);
      }

      // Get departments
      const departments = await Department.find({});
      const departmentMap = {};
      departments.forEach(dept => {
        departmentMap[dept._id.toString()] = {
          id: dept._id,
          name: dept.name,
          code: dept.code
        };
      });

      // Get payroll data
      const payrollRuns = await PayrollRun.find(payrollQuery)
        .populate({
          path: 'items',
          populate: { path: 'department', select: 'name code' }
        });

      // Get budget data
      const budgets = await Budget.find(budgetQuery);

      // Aggregate actual payroll by department
      const actualByDepartment = {};
      let totalActual = 0;

      payrollRuns.forEach(run => {
        run.items.forEach(item => {
          const deptId = item.department ? item.department._id.toString() : 'unassigned';

          if (!actualByDepartment[deptId]) {
            actualByDepartment[deptId] = {
              department: item.department ? {
                id: item.department._id,
                name: item.department.name,
                code: item.department.code
              } : { name: 'Unassigned', code: 'UNASSIGNED' },
              grossSalary: 0,
              benefits: 0,
              total: 0
            };
          }

          actualByDepartment[deptId].grossSalary += item.grossSalary || 0;
          actualByDepartment[deptId].benefits += item.totalBenefits || 0;
          actualByDepartment[deptId].total += (item.grossSalary || 0) + (item.totalBenefits || 0);
          totalActual += (item.grossSalary || 0) + (item.totalBenefits || 0);
        });
      });

      // Aggregate budget by department
      const budgetByDepartment = {};
      let totalBudget = 0;

      budgets.forEach(budget => {
        const deptId = budget.department ? budget.department.toString() : 'unassigned';

        if (!budgetByDepartment[deptId]) {
          budgetByDepartment[deptId] = {
            department: departmentMap[deptId] || { name: 'Unassigned', code: 'UNASSIGNED' },
            amount: 0
          };
        }

        budgetByDepartment[deptId].amount += budget.amount || 0;
        totalBudget += budget.amount || 0;
      });

      // Calculate variance
      const varianceByDepartment = {};
      let totalVariance = 0;

      // Process all departments from both actual and budget
      const allDepartmentIds = new Set([
        ...Object.keys(actualByDepartment),
        ...Object.keys(budgetByDepartment)
      ]);

      allDepartmentIds.forEach(deptId => {
        const actual = actualByDepartment[deptId]?.total || 0;
        const budget = budgetByDepartment[deptId]?.amount || 0;
        const variance = budget - actual;
        const variancePercent = budget > 0 ? (variance / budget) * 100 : 0;

        varianceByDepartment[deptId] = {
          department: actualByDepartment[deptId]?.department ||
                      budgetByDepartment[deptId]?.department ||
                      { name: 'Unassigned', code: 'UNASSIGNED' },
          actual,
          budget,
          variance,
          variancePercent,
          status: variance >= 0 ? 'under_budget' : 'over_budget'
        };

        totalVariance += variance;
      });

      // Prepare result
      return {
        period: {
          startDate,
          endDate,
          year: period?.year || now.getFullYear(),
          month: period?.month,
          quarter: period?.quarter
        },
        summary: {
          totalBudget,
          totalActual,
          totalVariance,
          totalVariancePercent: totalBudget > 0 ? (totalVariance / totalBudget) * 100 : 0,
          status: totalVariance >= 0 ? 'under_budget' : 'over_budget'
        },
        departments: Object.values(varianceByDepartment)
      };
    } catch (error) {
      logger.error('Error analyzing payroll budget variance', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }
}

// Create and export service instance
export const payrollIntegrationService = new PayrollIntegrationService();
