"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { cn } from "@/lib/utils"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import { Skeleton } from "@/components/ui/skeleton"
import { useEffect, useState, useCallback } from "react"
import { toast } from "@/components/ui/use-toast"
import {
  Check,
  X,
  RefreshCw,
  AlertCircle,
  Clock,
  CalendarDays,
  User,
  Building,
  TrendingUp,
  TrendingDown,
  Minus
} from "lucide-react"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import Link from "next/link"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { DashboardLeaveRequest, LeaveStats } from "@/lib/services/dashboard/leave-dashboard-service"
import { apiGet, apiRequest, ApiError } from "@/lib/utils/api-error-handler"

interface LeaveManagementProps {
  className?: string
}

// Remove static data - will be replaced with real API data

export function LeaveManagement({ className }: LeaveManagementProps) {
  const [leaveRequests, setLeaveRequests] = useState<DashboardLeaveRequest[]>([])
  const [leaveStats, setLeaveStats] = useState<LeaveStats | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date())
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date())
  // Removed useEmptyState hook - using simpler approach

  const fetchLeaveData = useCallback(async (showToast = false) => {
    setIsLoading(true)
    setError(null)

    try {
      // Fetch leave requests and stats in parallel using enhanced API handler
      const [requestsData, statsData] = await Promise.all([
        apiGet('/api/dashboard/leave?limit=10'),
        apiRequest('/api/dashboard/leave', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ period: 'month' })
        })
      ])

      if (requestsData.success && requestsData.data?.leaveRequests) {
        setLeaveRequests(requestsData.data.leaveRequests)
      }

      if (statsData.success && statsData.data) {
        setLeaveStats(statsData.data)
      }

      setLastRefresh(new Date())

      if (showToast) {
        toast({
          title: "Leave Data Updated",
          description: `Loaded ${requestsData.data?.leaveRequests?.length || 0} leave requests`,
          variant: "default",
        })
      }

    } catch (err) {
      console.error('Error fetching leave data:', err)

      let errorMessage = 'Failed to load leave data'
      if (err instanceof ApiError) {
        errorMessage = err.message
      } else if (err instanceof Error) {
        errorMessage = err.message
      }

      setError(errorMessage)

      if (showToast) {
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        })
      }
    } finally {
      setIsLoading(false)
    }
  }, [])

  useEffect(() => {
    fetchLeaveData()

    // Set up auto-refresh every 2 minutes
    const interval = setInterval(() => {
      fetchLeaveData()
    }, 120000)

    return () => clearInterval(interval)
  }, [fetchLeaveData])

  const handleRefresh = () => {
    fetchLeaveData(true)
  }

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'approved':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'rejected':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'cancelled':
        return 'bg-gray-100 text-gray-800 border-gray-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'urgent':
        return 'text-red-600'
      case 'high':
        return 'text-orange-600'
      case 'medium':
        return 'text-yellow-600'
      case 'low':
        return 'text-gray-600'
      default:
        return 'text-gray-600'
    }
  }

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    })
  }

  const formatRelativeTime = (date: Date) => {
    const now = new Date()
    const diff = now.getTime() - new Date(date).getTime()

    const minutes = Math.floor(diff / (1000 * 60))
    const hours = Math.floor(diff / (1000 * 60 * 60))
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))

    if (minutes < 1) return 'Just now'
    if (minutes < 60) return `${minutes}m ago`
    if (hours < 24) return `${hours}h ago`
    if (days < 7) return `${days}d ago`

    return new Date(date).toLocaleDateString()
  }

  const getLeaveRequestLink = (request: DashboardLeaveRequest) => {
    return `/dashboard/hr/leave/requests/${request.id}`
  }

  return (
    <Card className={cn("", className)}>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              Leave Management
              {leaveStats && (
                <Badge variant="secondary" className="text-xs">
                  {leaveStats.pendingRequests} pending
                </Badge>
              )}
              {leaveStats && leaveStats.overdueRequests > 0 && (
                <Badge variant="destructive" className="text-xs">
                  {leaveStats.overdueRequests} overdue
                </Badge>
              )}
            </CardTitle>
            <CardDescription>
              Track and manage employee leave requests
              {lastRefresh && (
                <span className="text-xs text-muted-foreground ml-2">
                  • Updated {lastRefresh.toLocaleTimeString()}
                </span>
              )}
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            {leaveStats && (
              <div className="flex items-center gap-4 text-xs text-muted-foreground">
                <div className="flex items-center gap-1">
                  <TrendingUp className="h-3 w-3 text-green-600" />
                  <span>{leaveStats.approvedRequests} approved</span>
                </div>
                <div className="flex items-center gap-1">
                  <Clock className="h-3 w-3 text-yellow-600" />
                  <span>{leaveStats.pendingRequests} pending</span>
                </div>
                <div className="flex items-center gap-1">
                  <TrendingDown className="h-3 w-3 text-red-600" />
                  <span>{leaveStats.rejectedRequests} rejected</span>
                </div>
              </div>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={handleRefresh}
              disabled={isLoading}
              className="h-8 w-8 p-0"
            >
              <RefreshCw className={cn("h-4 w-4", isLoading && "animate-spin")} />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="requests" className="space-y-4">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="requests">Leave Requests</TabsTrigger>
            <TabsTrigger value="calendar">Calendar View</TabsTrigger>
          </TabsList>

          <TabsContent value="requests">
            {isLoading ? (
              <div className="space-y-4">
                {Array.from({ length: 5 }).map((_, index) => (
                  <div key={index} className="flex items-start gap-4 rounded-lg border p-4">
                    <Skeleton className="h-9 w-9 rounded-full" />
                    <div className="flex-1 space-y-2">
                      <Skeleton className="h-4 w-3/4" />
                      <Skeleton className="h-3 w-1/2" />
                      <Skeleton className="h-3 w-1/4" />
                    </div>
                  </div>
                ))}
              </div>
            ) : error ? (
              <Alert>
                <AlertDescription>
                  {error}
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleRefresh}
                    className="ml-2"
                  >
                    Retry
                  </Button>
                </AlertDescription>
              </Alert>
            ) : leaveRequests.length === 0 ? (
              <div className="text-center py-8">
                <CalendarDays className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">No leave requests found</h3>
                <p className="text-muted-foreground">Leave requests will appear here when submitted</p>
              </div>
            ) : (
              <div className="space-y-4">
                {leaveRequests.map((request) => (
                  <div
                    key={request.id}
                    className="flex items-start gap-4 rounded-lg border p-4 hover:bg-muted/50 transition-colors group"
                  >
                    <div className="relative">
                      <Avatar className="h-9 w-9 border border-primary/10">
                        <AvatarImage
                          src={request.employee.avatar || "/placeholder.svg"}
                          alt={request.employee.name}
                        />
                        <AvatarFallback className="text-xs">
                          {request.employee.initials}
                        </AvatarFallback>
                      </Avatar>
                      {request.isOverdue && (
                        <div className="absolute -top-1 -right-1 h-3 w-3 bg-red-500 rounded-full border-2 border-background"></div>
                      )}
                    </div>

                    <div className="flex-1 space-y-1">
                      <div className="flex items-center justify-between">
                        <Link
                          href={getLeaveRequestLink(request)}
                          className="text-sm font-medium leading-none hover:underline"
                        >
                          {request.employee.name}
                        </Link>
                        <div className="flex items-center gap-1">
                          <Badge className={cn("text-xs", getStatusColor(request.status))}>
                            {request.status.charAt(0).toUpperCase() + request.status.slice(1)}
                          </Badge>
                          {request.urgency !== 'low' && (
                            <Badge variant="outline" className={cn("text-xs", getUrgencyColor(request.urgency))}>
                              {request.urgency}
                            </Badge>
                          )}
                        </div>
                      </div>

                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <span
                          className="inline-block w-3 h-3 rounded-full"
                          style={{ backgroundColor: request.leaveType.color }}
                        ></span>
                        <span>{request.leaveType.name}</span>
                        <span>•</span>
                        <span>{request.duration} day{request.duration !== 1 ? 's' : ''}</span>
                        {request.employee.department && (
                          <>
                            <span>•</span>
                            <span>{request.employee.department}</span>
                          </>
                        )}
                      </div>

                      <p className="text-xs text-muted-foreground">
                        {formatDate(request.startDate)} to {formatDate(request.endDate)}
                      </p>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3 text-xs text-muted-foreground">
                          <span className="flex items-center gap-1">
                            <Clock className={cn("h-3 w-3", getUrgencyColor(request.urgency))} />
                            {formatRelativeTime(request.submittedAt)}
                          </span>
                          {request.daysUntilStart >= 0 && (
                            <span className="flex items-center gap-1">
                              <CalendarDays className="h-3 w-3" />
                              {request.daysUntilStart === 0 ? 'Starts today' :
                               request.daysUntilStart === 1 ? 'Starts tomorrow' :
                               `Starts in ${request.daysUntilStart} days`}
                            </span>
                          )}
                        </div>

                        {request.status === 'pending' && (
                          <div className="flex gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
                            <Button size="sm" variant="outline" className="h-8 w-8 p-0 text-green-600 hover:text-green-700">
                              <Check className="h-4 w-4" />
                            </Button>
                            <Button size="sm" variant="outline" className="h-8 w-8 p-0 text-red-600 hover:text-red-700">
                              <X className="h-4 w-4" />
                            </Button>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="calendar">
            <div className="rounded-md border">
              <Calendar
                mode="single"
                selected={selectedDate}
                onSelect={setSelectedDate}
                className="rounded-md"
              />
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
