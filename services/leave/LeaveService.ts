import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { errorService } from '@/lib/backend/services/error-service';
import mongoose from 'mongoose';
import { differenceInBusinessDays, addDays, format } from 'date-fns';
import Leave from '@/models/leave/Leave';
import LeaveType, { ILeaveType } from '@/models/leave/LeaveType';
import LeaveBalance, { ILeaveBalance } from '@/models/leave/LeaveBalance';
import Employee from '@/models/Employee';

/**
 * Service for managing leave requests and balances
 */
export class LeaveService {
  /**
   * Create a new leave request
   * @param data - Leave request data
   * @param userId - User ID creating the request
   * @returns Created leave request
   */
  async createLeaveRequest(data: any, userId: string): Promise<any> {
    try {
      await connectToDatabase();
      logger.info('Creating new leave request', LogCategory.HR, { data });
      
      // Check if employee exists
      const employee = await Employee.findById(data.employeeId);
      if (!employee) {
        throw new Error(`Employee with ID ${data.employeeId} not found`);
      }
      
      // Check if leave type exists
      const leaveType = await LeaveType.findById(data.leaveTypeId);
      if (!leaveType) {
        throw new Error(`Leave type with ID ${data.leaveTypeId} not found`);
      }
      
      // Calculate duration in days
      const startDate = new Date(data.startDate);
      const endDate = new Date(data.endDate);
      
      // Ensure start date is before end date
      if (startDate > endDate) {
        throw new Error('Start date must be before end date');
      }
      
      // Calculate duration in business days
      const duration = differenceInBusinessDays(addDays(endDate, 1), startDate);
      
      if (duration <= 0) {
        throw new Error('Leave duration must be at least 1 day');
      }
      
      // Check if leave type has maximum consecutive days limit
      if (leaveType.maxConsecutiveDays > 0 && duration > leaveType.maxConsecutiveDays) {
        throw new Error(`Maximum consecutive days for ${leaveType.name} is ${leaveType.maxConsecutiveDays}`);
      }
      
      // Check if leave type requires minimum notice
      if (leaveType.minNoticeInDays > 0) {
        const today = new Date();
        const daysNotice = differenceInBusinessDays(startDate, today);
        
        if (daysNotice < leaveType.minNoticeInDays) {
          throw new Error(`Minimum notice for ${leaveType.name} is ${leaveType.minNoticeInDays} days`);
        }
      }
      
      // Check leave balance if leave type is active
      if (leaveType.isActive) {
        const currentYear = new Date().getFullYear();
        
        // Get or create leave balance for the current year
        let leaveBalance = await this.getOrCreateLeaveBalance(
          data.employeeId,
          data.leaveTypeId,
          currentYear,
          userId
        );
        
        // Check if employee has enough leave balance
        if (leaveBalance.remainingDays < duration) {
          throw new Error(`Insufficient leave balance. Available: ${leaveBalance.remainingDays} days, Requested: ${duration} days`);
        }
        
        // Update leave balance (pending days)
        leaveBalance.pendingDays += duration;
        leaveBalance.remainingDays = leaveBalance.totalDays - leaveBalance.usedDays - leaveBalance.pendingDays;
        leaveBalance.updatedBy = new mongoose.Types.ObjectId(userId);
        await leaveBalance.save();
      }
      
      // Generate leave ID
      const leaveId = await this.generateLeaveId();
      
      // Create leave request
      const leaveRequest = new Leave({
        leaveId,
        employeeId: data.employeeId,
        leaveTypeId: data.leaveTypeId,
        startDate,
        endDate,
        duration,
        reason: data.reason,
        status: 'pending',
        attachments: data.attachments || [],
        notes: data.notes,
        createdBy: new mongoose.Types.ObjectId(userId),
      });
      
      await leaveRequest.save();
      
      return leaveRequest;
    } catch (error) {
      logger.error('Error creating leave request', LogCategory.HR, error);
      throw error;
    }
  }

  /**
   * Update a leave request
   * @param id - Leave request ID
   * @param data - Update data
   * @param userId - User ID updating the request
   * @returns Updated leave request
   */
  async updateLeaveRequest(id: string, data: any, userId: string): Promise<any> {
    try {
      await connectToDatabase();
      logger.info('Updating leave request', LogCategory.HR, { id, data });

      // Get existing leave request
      const existingRequest = await Leave.findById(id);
      if (!existingRequest) {
        throw new Error(`Leave request with ID ${id} not found`);
      }

      // Check if request is still pending
      if (existingRequest.status !== 'pending') {
        throw new Error(`Cannot update leave request with status: ${existingRequest.status}`);
      }

      // If dates are being updated, recalculate duration and validate
      let duration = existingRequest.duration;
      let startDate = existingRequest.startDate;
      let endDate = existingRequest.endDate;

      if (data.startDate || data.endDate) {
        startDate = data.startDate ? new Date(data.startDate) : existingRequest.startDate;
        endDate = data.endDate ? new Date(data.endDate) : existingRequest.endDate;

        // Ensure start date is before end date
        if (startDate > endDate) {
          throw new Error('Start date must be before end date');
        }

        // Calculate new duration
        duration = differenceInBusinessDays(addDays(endDate, 1), startDate);

        if (duration <= 0) {
          throw new Error('Leave duration must be at least 1 day');
        }
      }

      // If leave type is being updated, validate it
      let leaveType = null;
      if (data.leaveTypeId) {
        leaveType = await LeaveType.findById(data.leaveTypeId);
        if (!leaveType) {
          throw new Error(`Leave type with ID ${data.leaveTypeId} not found`);
        }

        // Check constraints for new leave type
        if (leaveType.maxConsecutiveDays > 0 && duration > leaveType.maxConsecutiveDays) {
          throw new Error(`Maximum consecutive days for ${leaveType.name} is ${leaveType.maxConsecutiveDays}`);
        }

        if (leaveType.minNoticeInDays > 0) {
          const today = new Date();
          const daysNotice = differenceInBusinessDays(startDate, today);

          if (daysNotice < leaveType.minNoticeInDays) {
            throw new Error(`Minimum notice for ${leaveType.name} is ${leaveType.minNoticeInDays} days`);
          }
        }
      }

      // Update leave balance if duration or leave type changed
      const currentYear = new Date().getFullYear();
      const oldLeaveBalance = await LeaveBalance.findOne({
        employeeId: existingRequest.employeeId,
        leaveTypeId: existingRequest.leaveTypeId,
        year: currentYear,
      });

      if (oldLeaveBalance) {
        // Restore old pending days
        oldLeaveBalance.pendingDays -= existingRequest.duration;
        oldLeaveBalance.remainingDays = oldLeaveBalance.totalDays - oldLeaveBalance.usedDays - oldLeaveBalance.pendingDays;
        await oldLeaveBalance.save();
      }

      // Check new leave balance
      const newLeaveTypeId = data.leaveTypeId || existingRequest.leaveTypeId;
      const newLeaveBalance = await this.getOrCreateLeaveBalance(
        existingRequest.employeeId.toString(),
        newLeaveTypeId.toString(),
        currentYear,
        userId
      );

      if (newLeaveBalance.remainingDays < duration) {
        // Restore old balance before throwing error
        if (oldLeaveBalance) {
          oldLeaveBalance.pendingDays += existingRequest.duration;
          oldLeaveBalance.remainingDays = oldLeaveBalance.totalDays - oldLeaveBalance.usedDays - oldLeaveBalance.pendingDays;
          await oldLeaveBalance.save();
        }
        throw new Error(`Insufficient leave balance. Available: ${newLeaveBalance.remainingDays} days, Requested: ${duration} days`);
      }

      // Update new leave balance
      newLeaveBalance.pendingDays += duration;
      newLeaveBalance.remainingDays = newLeaveBalance.totalDays - newLeaveBalance.usedDays - newLeaveBalance.pendingDays;
      newLeaveBalance.updatedBy = new mongoose.Types.ObjectId(userId);
      await newLeaveBalance.save();

      // Update the leave request
      const updateData: any = {
        updatedBy: new mongoose.Types.ObjectId(userId),
      };

      if (data.leaveTypeId) updateData.leaveTypeId = data.leaveTypeId;
      if (data.startDate) updateData.startDate = startDate;
      if (data.endDate) updateData.endDate = endDate;
      if (data.reason) updateData.reason = data.reason;
      if (data.notes !== undefined) updateData.notes = data.notes;
      if (data.attachments !== undefined) updateData.attachments = data.attachments;
      if (duration !== existingRequest.duration) updateData.duration = duration;

      const updatedRequest = await Leave.findByIdAndUpdate(
        id,
        updateData,
        { new: true, runValidators: true }
      ).populate('leaveTypeId', 'name code color')
       .populate('employeeId', 'firstName lastName position')
       .populate('approvedBy', 'firstName lastName');

      return updatedRequest;
    } catch (error) {
      logger.error('Error updating leave request', LogCategory.HR, error);
      throw error;
    }
  }

  /**
   * Update a leave request status
   * @param id - Leave request ID
   * @param status - New status
   * @param userId - User ID updating the request
   * @param reason - Reason for rejection (if status is 'rejected')
   * @returns Updated leave request
   */
  async updateLeaveRequestStatus(id: string, status: 'approved' | 'rejected' | 'cancelled', userId: string, reason?: string): Promise<any> {
    try {
      await connectToDatabase();
      logger.info('Updating leave request status', LogCategory.HR, { id, status });
      
      // Get leave request
      const leaveRequest = await Leave.findById(id);
      if (!leaveRequest) {
        throw new Error(`Leave request with ID ${id} not found`);
      }
      
      // Check if leave request is already in the requested status
      if (leaveRequest.status === status) {
        return leaveRequest;
      }
      
      // Check if leave request is already approved or rejected
      if (leaveRequest.status === 'approved' || leaveRequest.status === 'rejected') {
        throw new Error(`Cannot update status of a leave request that is already ${leaveRequest.status}`);
      }
      
      // Update leave request status
      leaveRequest.status = status;
      
      if (status === 'approved') {
        leaveRequest.approvedBy = new mongoose.Types.ObjectId(userId);
        leaveRequest.approvalDate = new Date();
      } else if (status === 'rejected') {
        if (!reason) {
          throw new Error('Reason is required for rejection');
        }
        leaveRequest.rejectionReason = reason;
      }
      
      await leaveRequest.save();
      
      // Update leave balance
      const currentYear = new Date().getFullYear();
      const leaveBalance = await LeaveBalance.findOne({
        employeeId: leaveRequest.employeeId,
        leaveTypeId: leaveRequest.leaveTypeId,
        year: currentYear,
      });
      
      if (leaveBalance) {
        if (status === 'approved') {
          // Move days from pending to used
          leaveBalance.pendingDays -= leaveRequest.duration;
          leaveBalance.usedDays += leaveRequest.duration;
        } else {
          // Remove days from pending
          leaveBalance.pendingDays -= leaveRequest.duration;
        }
        
        // Recalculate remaining days
        leaveBalance.remainingDays = leaveBalance.totalDays - leaveBalance.usedDays - leaveBalance.pendingDays;
        leaveBalance.updatedBy = new mongoose.Types.ObjectId(userId);
        await leaveBalance.save();
      }
      
      return leaveRequest;
    } catch (error) {
      logger.error('Error updating leave request status', LogCategory.HR, error);
      throw error;
    }
  }

  /**
   * Get or create leave balance for an employee
   * @param employeeId - Employee ID
   * @param leaveTypeId - Leave type ID
   * @param year - Year
   * @param userId - User ID creating the balance
   * @returns Leave balance
   */
  async getOrCreateLeaveBalance(employeeId: string, leaveTypeId: string, year: number, userId: string): Promise<ILeaveBalance> {
    try {
      await connectToDatabase();
      
      // Check if leave balance exists
      let leaveBalance = await LeaveBalance.findOne({
        employeeId: new mongoose.Types.ObjectId(employeeId),
        leaveTypeId: new mongoose.Types.ObjectId(leaveTypeId),
        year,
      });
      
      if (leaveBalance) {
        return leaveBalance;
      }
      
      // Get leave type
      const leaveType = await LeaveType.findById(leaveTypeId);
      if (!leaveType) {
        throw new Error(`Leave type with ID ${leaveTypeId} not found`);
      }
      
      // Create new leave balance
      leaveBalance = new LeaveBalance({
        employeeId: new mongoose.Types.ObjectId(employeeId),
        leaveTypeId: new mongoose.Types.ObjectId(leaveTypeId),
        year,
        totalDays: leaveType.defaultDays,
        usedDays: 0,
        pendingDays: 0,
        remainingDays: leaveType.defaultDays,
        carryOverDays: 0,
        createdBy: new mongoose.Types.ObjectId(userId),
      });
      
      await leaveBalance.save();
      
      return leaveBalance;
    } catch (error) {
      logger.error('Error getting or creating leave balance', LogCategory.HR, error);
      throw error;
    }
  }

  /**
   * Get leave balances for an employee
   * @param employeeId - Employee ID
   * @param year - Year (default: current year)
   * @returns Leave balances
   */
  async getLeaveBalances(employeeId: string, year: number = new Date().getFullYear()): Promise<any[]> {
    try {
      await connectToDatabase();
      
      // Get leave balances
      const leaveBalances = await LeaveBalance.find({
        employeeId: new mongoose.Types.ObjectId(employeeId),
        year,
      }).populate('leaveTypeId');
      
      // Get all leave types
      const leaveTypes = await LeaveType.find({ isActive: true });
      
      // Create result array with all leave types
      const result = leaveTypes.map(leaveType => {
        // Find balance for this leave type
        const balance = leaveBalances.find(
          b => b.leaveTypeId._id.toString() === leaveType._id.toString()
        );
        
        if (balance) {
          return {
            leaveType: {
              _id: leaveType._id,
              name: leaveType.name,
              code: leaveType.code,
              color: leaveType.color,
            },
            totalDays: balance.totalDays,
            usedDays: balance.usedDays,
            pendingDays: balance.pendingDays,
            remainingDays: balance.remainingDays,
            carryOverDays: balance.carryOverDays,
          };
        } else {
          return {
            leaveType: {
              _id: leaveType._id,
              name: leaveType.name,
              code: leaveType.code,
              color: leaveType.color,
            },
            totalDays: leaveType.defaultDays,
            usedDays: 0,
            pendingDays: 0,
            remainingDays: leaveType.defaultDays,
            carryOverDays: 0,
          };
        }
      });
      
      return result;
    } catch (error) {
      logger.error('Error getting leave balances', LogCategory.HR, error);
      throw error;
    }
  }

  /**
   * Process year-end leave carryover
   * @param fromYear - From year
   * @param toYear - To year
   * @param userId - User ID processing the carryover
   * @returns Number of processed employees
   */
  async processYearEndCarryover(fromYear: number, toYear: number, userId: string): Promise<number> {
    try {
      await connectToDatabase();
      logger.info('Processing year-end leave carryover', LogCategory.HR, { fromYear, toYear });
      
      // Get all leave types that allow carryover
      const leaveTypes = await LeaveType.find({
        isActive: true,
        allowCarryOver: true,
      });
      
      let processedCount = 0;
      
      // Process each leave type
      for (const leaveType of leaveTypes) {
        // Get all leave balances for this leave type and year
        const leaveBalances = await LeaveBalance.find({
          leaveTypeId: leaveType._id,
          year: fromYear,
        });
        
        // Process each leave balance
        for (const balance of leaveBalances) {
          // Calculate carryover days
          let carryOverDays = balance.remainingDays;
          
          // Apply maximum carryover limit if set
          if (leaveType.maxCarryOverDays > 0 && carryOverDays > leaveType.maxCarryOverDays) {
            carryOverDays = leaveType.maxCarryOverDays;
          }
          
          if (carryOverDays > 0) {
            // Get or create leave balance for the next year
            const nextYearBalance = await this.getOrCreateLeaveBalance(
              balance.employeeId.toString(),
              leaveType._id.toString(),
              toYear,
              userId
            );
            
            // Update carryover days
            nextYearBalance.carryOverDays = carryOverDays;
            nextYearBalance.totalDays = leaveType.defaultDays + carryOverDays;
            nextYearBalance.remainingDays = nextYearBalance.totalDays - nextYearBalance.usedDays - nextYearBalance.pendingDays;
            nextYearBalance.updatedBy = new mongoose.Types.ObjectId(userId);
            await nextYearBalance.save();
            
            processedCount++;
          }
        }
      }
      
      return processedCount;
    } catch (error) {
      logger.error('Error processing year-end leave carryover', LogCategory.HR, error);
      throw error;
    }
  }

  /**
   * Generate a unique leave ID
   * @returns Generated leave ID
   */
  private async generateLeaveId(): Promise<string> {
    const currentYear = new Date().getFullYear().toString().substr(-2);
    const currentMonth = (new Date().getMonth() + 1).toString().padStart(2, '0');
    
    // Get count of leave requests created this month
    const count = await Leave.countDocuments({
      leaveId: { $regex: `^LV${currentYear}${currentMonth}` }
    });
    
    // Generate leave ID (LV + YY + MM + 4-digit sequential number)
    return `LV${currentYear}${currentMonth}${(count + 1).toString().padStart(4, '0')}`;
  }
}

// Export singleton instance
export const leaveService = new LeaveService();
