import mongoose from 'mongoose';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import Employee from '@/models/Employee';
import Leave from '@/models/leave/Leave';
import LeaveType from '@/models/leave/LeaveType';
import LeaveEncashment from '@/models/leave/LeaveEncashment';
import { startOfMonth, endOfMonth, format } from 'date-fns';

export interface PayrollLeaveDeduction {
  employeeId: string;
  employeeName: string;
  employeeCode: string;
  leaveRequests: {
    id: string;
    leaveType: string;
    startDate: string;
    endDate: string;
    days: number;
    isPaid: boolean;
    deductionAmount: number;
  }[];
  totalUnpaidDays: number;
  totalDeductionAmount: number;
  basicSalary: number;
  dailyRate: number;
}

export interface PayrollEncashmentPayment {
  employeeId: string;
  employeeName: string;
  employeeCode: string;
  encashments: {
    id: string;
    encashmentId: string;
    leaveType: string;
    daysEncashed: number;
    ratePerDay: number;
    totalAmount: number;
    taxable: boolean;
    payrollComponent: string;
  }[];
  totalEncashmentAmount: number;
  taxableAmount: number;
  nonTaxableAmount: number;
}

export interface PayrollPeriodData {
  periodStart: string;
  periodEnd: string;
  totalEmployees: number;
  leaveDeductions: PayrollLeaveDeduction[];
  encashmentPayments: PayrollEncashmentPayment[];
  summary: {
    totalLeaveDeductions: number;
    totalEncashmentPayments: number;
    netPayrollImpact: number;
  };
}

export interface CostCenterAllocation {
  costCenter: string;
  departmentId: string;
  departmentName: string;
  totalDeductions: number;
  totalEncashments: number;
  netAmount: number;
  employeeCount: number;
}

class PayrollIntegrationService {
  /**
   * Generate payroll data for a specific period
   * @param periodStart - Start date of payroll period
   * @param periodEnd - End date of payroll period
   * @returns Payroll period data
   */
  async generatePayrollData(periodStart: Date, periodEnd: Date): Promise<PayrollPeriodData> {
    try {
      await connectToDatabase();
      logger.info('Generating payroll data', LogCategory.PAYROLL, { periodStart, periodEnd });

      // Get all active employees
      const employees = await Employee.find({ isActive: true });

      const leaveDeductions: PayrollLeaveDeduction[] = [];
      const encashmentPayments: PayrollEncashmentPayment[] = [];

      for (const employee of employees) {
        // Calculate leave deductions
        const deductionData = await this.calculateLeaveDeductions(
          employee,
          periodStart,
          periodEnd
        );
        
        if (deductionData.totalUnpaidDays > 0) {
          leaveDeductions.push(deductionData);
        }

        // Calculate encashment payments
        const encashmentData = await this.calculateEncashmentPayments(
          employee,
          periodStart,
          periodEnd
        );
        
        if (encashmentData.totalEncashmentAmount > 0) {
          encashmentPayments.push(encashmentData);
        }
      }

      // Calculate summary
      const totalLeaveDeductions = leaveDeductions.reduce(
        (sum, emp) => sum + emp.totalDeductionAmount, 0
      );
      
      const totalEncashmentPayments = encashmentPayments.reduce(
        (sum, emp) => sum + emp.totalEncashmentAmount, 0
      );

      return {
        periodStart: format(periodStart, 'yyyy-MM-dd'),
        periodEnd: format(periodEnd, 'yyyy-MM-dd'),
        totalEmployees: employees.length,
        leaveDeductions,
        encashmentPayments,
        summary: {
          totalLeaveDeductions,
          totalEncashmentPayments,
          netPayrollImpact: totalEncashmentPayments - totalLeaveDeductions
        }
      };
    } catch (error) {
      logger.error('Error generating payroll data', LogCategory.PAYROLL, error);
      throw error;
    }
  }

  /**
   * Calculate leave deductions for an employee
   * @param employee - Employee document
   * @param periodStart - Period start date
   * @param periodEnd - Period end date
   * @returns Leave deduction data
   */
  private async calculateLeaveDeductions(
    employee: any,
    periodStart: Date,
    periodEnd: Date
  ): Promise<PayrollLeaveDeduction> {
    // Get approved leave requests that fall within the payroll period
    const leaveRequests = await Leave.find({
      employeeId: employee._id,
      status: 'approved',
      $or: [
        {
          startDate: { $gte: periodStart, $lte: periodEnd }
        },
        {
          endDate: { $gte: periodStart, $lte: periodEnd }
        },
        {
          startDate: { $lte: periodStart },
          endDate: { $gte: periodEnd }
        }
      ]
    }).populate('leaveTypeId', 'name isPaid code');

    const dailyRate = this.calculateDailyRate(employee);
    let totalUnpaidDays = 0;
    let totalDeductionAmount = 0;

    const processedRequests = leaveRequests.map(request => {
      const leaveType = request.leaveTypeId as any;
      const isPaid = leaveType.isPaid;
      
      // Calculate overlapping days within the payroll period
      const requestStart = new Date(request.startDate);
      const requestEnd = new Date(request.endDate);
      const overlapStart = new Date(Math.max(requestStart.getTime(), periodStart.getTime()));
      const overlapEnd = new Date(Math.min(requestEnd.getTime(), periodEnd.getTime()));
      
      const overlapDays = Math.max(0, Math.ceil((overlapEnd.getTime() - overlapStart.getTime()) / (1000 * 60 * 60 * 24)) + 1);
      
      let deductionAmount = 0;
      if (!isPaid && overlapDays > 0) {
        deductionAmount = overlapDays * dailyRate;
        totalUnpaidDays += overlapDays;
        totalDeductionAmount += deductionAmount;
      }

      return {
        id: request._id.toString(),
        leaveType: leaveType.name,
        startDate: format(requestStart, 'yyyy-MM-dd'),
        endDate: format(requestEnd, 'yyyy-MM-dd'),
        days: overlapDays,
        isPaid,
        deductionAmount
      };
    }).filter(req => req.days > 0);

    return {
      employeeId: employee._id.toString(),
      employeeName: `${employee.firstName} ${employee.lastName}`,
      employeeCode: employee.employeeCode || employee._id.toString(),
      leaveRequests: processedRequests,
      totalUnpaidDays,
      totalDeductionAmount,
      basicSalary: employee.basicSalary || 0,
      dailyRate
    };
  }

  /**
   * Calculate encashment payments for an employee
   * @param employee - Employee document
   * @param periodStart - Period start date
   * @param periodEnd - Period end date
   * @returns Encashment payment data
   */
  private async calculateEncashmentPayments(
    employee: any,
    periodStart: Date,
    periodEnd: Date
  ): Promise<PayrollEncashmentPayment> {
    // Get approved encashments that should be paid in this period
    const encashments = await LeaveEncashment.find({
      employeeId: employee._id,
      status: 'approved',
      encashmentDate: { $gte: periodStart, $lte: periodEnd }
    }).populate('leaveTypeId', 'name')
      .populate('encashmentRuleId', 'payrollIntegration');

    let totalEncashmentAmount = 0;
    let taxableAmount = 0;
    let nonTaxableAmount = 0;

    const processedEncashments = encashments.map(encashment => {
      const leaveType = encashment.leaveTypeId as any;
      const rule = encashment.encashmentRuleId as any;
      
      const isTaxable = rule?.payrollIntegration?.taxable ?? true;
      const payrollComponent = rule?.payrollIntegration?.payrollComponent || 'LEAVE_ENCASHMENT';
      
      totalEncashmentAmount += encashment.totalAmount;
      
      if (isTaxable) {
        taxableAmount += encashment.totalAmount;
      } else {
        nonTaxableAmount += encashment.totalAmount;
      }

      return {
        id: encashment._id.toString(),
        encashmentId: encashment.encashmentId,
        leaveType: leaveType.name,
        daysEncashed: encashment.daysEncashed,
        ratePerDay: encashment.ratePerDay,
        totalAmount: encashment.totalAmount,
        taxable: isTaxable,
        payrollComponent
      };
    });

    return {
      employeeId: employee._id.toString(),
      employeeName: `${employee.firstName} ${employee.lastName}`,
      employeeCode: employee.employeeCode || employee._id.toString(),
      encashments: processedEncashments,
      totalEncashmentAmount,
      taxableAmount,
      nonTaxableAmount
    };
  }

  /**
   * Generate cost center allocation report
   * @param periodStart - Period start date
   * @param periodEnd - Period end date
   * @returns Cost center allocations
   */
  async generateCostCenterAllocation(
    periodStart: Date,
    periodEnd: Date
  ): Promise<CostCenterAllocation[]> {
    try {
      await connectToDatabase();
      logger.info('Generating cost center allocation', LogCategory.PAYROLL, { periodStart, periodEnd });

      // Get payroll data
      const payrollData = await this.generatePayrollData(periodStart, periodEnd);

      // Group by department
      const departmentMap = new Map<string, {
        departmentId: string;
        departmentName: string;
        totalDeductions: number;
        totalEncashments: number;
        employeeCount: number;
      }>();

      // Process leave deductions
      for (const deduction of payrollData.leaveDeductions) {
        const employee = await Employee.findById(deduction.employeeId).populate('departmentId', 'name costCenter');
        if (employee && employee.departmentId) {
          const deptId = employee.departmentId._id.toString();
          const deptName = (employee.departmentId as any).name;

          if (!departmentMap.has(deptId)) {
            departmentMap.set(deptId, {
              departmentId: deptId,
              departmentName: deptName,
              totalDeductions: 0,
              totalEncashments: 0,
              employeeCount: 0
            });
          }

          const dept = departmentMap.get(deptId)!;
          dept.totalDeductions += deduction.totalDeductionAmount;
          dept.employeeCount++;
        }
      }

      // Process encashment payments
      for (const encashment of payrollData.encashmentPayments) {
        const employee = await Employee.findById(encashment.employeeId).populate('departmentId', 'name costCenter');
        if (employee && employee.departmentId) {
          const deptId = employee.departmentId._id.toString();
          const deptName = (employee.departmentId as any).name;

          if (!departmentMap.has(deptId)) {
            departmentMap.set(deptId, {
              departmentId: deptId,
              departmentName: deptName,
              totalDeductions: 0,
              totalEncashments: 0,
              employeeCount: 0
            });
          }

          const dept = departmentMap.get(deptId)!;
          dept.totalEncashments += encashment.totalEncashmentAmount;
        }
      }

      // Convert to array and add cost center info
      const allocations: CostCenterAllocation[] = [];

      for (const [deptId, data] of departmentMap) {
        const Department = (await import('@/models/Department')).default;
        const department = await Department.findById(deptId);

        allocations.push({
          costCenter: department?.costCenter || deptId,
          departmentId: data.departmentId,
          departmentName: data.departmentName,
          totalDeductions: data.totalDeductions,
          totalEncashments: data.totalEncashments,
          netAmount: data.totalEncashments - data.totalDeductions,
          employeeCount: data.employeeCount
        });
      }

      return allocations.sort((a, b) => a.departmentName.localeCompare(b.departmentName));
    } catch (error) {
      logger.error('Error generating cost center allocation', LogCategory.PAYROLL, error);
      throw error;
    }
  }

  /**
   * Calculate daily rate for an employee
   * @param employee - Employee document
   * @returns Daily rate
   */
  private calculateDailyRate(employee: any): number {
    const monthlySalary = employee.basicSalary || 0;
    return monthlySalary / 30; // Assuming 30 days per month
  }

  /**
   * Mark encashments as processed for payroll
   * @param encashmentIds - Array of encashment IDs
   * @param payrollRunId - Payroll run ID
   * @param userId - User ID processing
   * @returns Number of encashments processed
   */
  async markEncashmentsAsProcessed(
    encashmentIds: string[],
    payrollRunId: string,
    userId: string
  ): Promise<number> {
    try {
      await connectToDatabase();
      logger.info('Marking encashments as processed', LogCategory.PAYROLL, { encashmentIds, payrollRunId });

      const result = await LeaveEncashment.updateMany(
        {
          _id: { $in: encashmentIds.map(id => new mongoose.Types.ObjectId(id)) },
          status: 'approved'
        },
        {
          status: 'processed',
          payrollRunId: new mongoose.Types.ObjectId(payrollRunId),
          payrollProcessedDate: new Date(),
          updatedBy: new mongoose.Types.ObjectId(userId)
        }
      );

      return result.modifiedCount;
    } catch (error) {
      logger.error('Error marking encashments as processed', LogCategory.PAYROLL, error);
      throw error;
    }
  }
}

// Export singleton instance
export const payrollIntegrationService = new PayrollIntegrationService();
