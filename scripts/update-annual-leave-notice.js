/**
 * <PERSON><PERSON>t to update Annual Leave minimum notice period
 * This script reduces the minimum notice period from 7 to 5 business days
 */

const mongoose = require('mongoose');
require('dotenv').config({ path: '.env.local' });

// MongoDB connection
const connectToDatabase = async () => {
  try {
    const mongoUri = process.env.MONGODB_URI;
    if (!mongoUri) {
      throw new Error('MONGODB_URI environment variable is not set');
    }

    console.log('🔗 Connecting to MongoDB...');
    console.log('📍 URI:', mongoUri.replace(/\/\/[^:]+:[^@]+@/, '//***:***@')); // Hide credentials

    if (mongoose.connection.readyState === 0) {
      await mongoose.connect(mongoUri);
      console.log('✅ Connected to MongoDB');
    }
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

// LeaveType schema (simplified)
const LeaveTypeSchema = new mongoose.Schema({
  name: String,
  code: String,
  description: String,
  defaultDays: Number,
  isActive: Boolean,
  isPaid: Boolean,
  requiresApproval: Boolean,
  maxConsecutiveDays: Number,
  minNoticeInDays: Number,
  allowCarryOver: Boolean,
  maxCarryOverDays: Number,
  color: String,
  applicableRoles: [String],
  applicableDepartments: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Department' }],
  createdBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
  updatedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' }
}, {
  timestamps: true
});

const LeaveType = mongoose.models.LeaveType || mongoose.model('LeaveType', LeaveTypeSchema);

async function updateAnnualLeaveNotice() {
  try {
    await connectToDatabase();
    
    console.log('🔍 Looking for Annual Leave type...');
    
    // Find the Annual Leave type
    const annualLeave = await LeaveType.findOne({ code: 'ANNUAL' });
    
    if (!annualLeave) {
      console.log('❌ Annual Leave type not found');
      return;
    }
    
    console.log(`📋 Found Annual Leave type:`);
    console.log(`   - Name: ${annualLeave.name}`);
    console.log(`   - Code: ${annualLeave.code}`);
    console.log(`   - Current minimum notice: ${annualLeave.minNoticeInDays} days`);
    
    if (annualLeave.minNoticeInDays === 5) {
      console.log('✅ Annual Leave minimum notice is already set to 5 days');
      return;
    }
    
    // Update the minimum notice period
    const result = await LeaveType.findByIdAndUpdate(
      annualLeave._id,
      { 
        minNoticeInDays: 5,
        updatedBy: annualLeave.createdBy // Use the same user who created it
      },
      { new: true }
    );
    
    if (result) {
      console.log('✅ Successfully updated Annual Leave minimum notice period');
      console.log(`   - Old value: ${annualLeave.minNoticeInDays} days`);
      console.log(`   - New value: ${result.minNoticeInDays} days`);
    } else {
      console.log('❌ Failed to update Annual Leave type');
    }
    
  } catch (error) {
    console.error('❌ Error updating Annual Leave notice period:', error);
  } finally {
    await mongoose.connection.close();
    console.log('🔌 Database connection closed');
  }
}

// Run the script
updateAnnualLeaveNotice();
